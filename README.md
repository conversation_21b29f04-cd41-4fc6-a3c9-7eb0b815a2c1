# MusicDou - 音乐站点后端系统

## 项目概述

MusicDou 是一个基于 Node.js 开发的音乐站点后端系统，采用单体架构设计，提供完整的音乐管理、用户管理、歌单管理和第三方平台集成功能。

## 核心功能

### 1. 用户管理系统

#### 用户功能
- 用户注册/登录/注销
- 用户资料管理
- 密码修改和找回
- 用户头像上传

#### 用户组和权限
- **管理员组**: 系统管理权限，用户管理，内容审核
- **VIP用户组**: 高级功能权限，无广告，高品质音乐下载
- **普通用户组**: 基础功能权限

> 注意：系统不设置访客组，所有用户必须登录后才能访问任何功能接口

#### 用户积分系统
- 注册奖励积分
- 每日签到积分
- 上传音乐奖励积分
- 分享歌单奖励积分
- 积分兑换VIP时长
- 积分商城功能

### 2. 音乐管理系统

#### 音乐上传功能
- 支持多种音频格式 (MP3, FLAC, WAV, AAC)
- 自动音质检测和分析
  - 比特率检测 (128k, 192k, 320k, 无损等)
  - 采样率检测
  - 音频时长获取
  - 文件大小限制
- 音乐元数据提取
  - 歌曲名称、艺术家、专辑
  - 封面图片提取
  - 发行年份、流派等信息
- 音乐文件存储 (MinIO对象存储)

#### 音乐质量管理
- 音质等级分类
  - 标准品质 (128kbps)
  - 高品质 (192kbps)
  - 超高品质 (320kbps)
  - 无损品质 (FLAC)
- 重复音乐检测和去重
- 音乐审核机制

### 3. 歌单管理系统

#### 歌单功能
- 创建/编辑/删除歌单
- 歌单封面设置
- 歌单描述和标签
- 歌单公开/私有设置
- 歌单分享功能

#### 歌曲管理
- 添加歌曲到歌单
- 从歌单移除歌曲
- 歌单内歌曲排序
- 歌曲可加入多个歌单
- 默认歌单自动创建

#### 歌单分类
- **我创建的歌单**: 用户自己创建的歌单列表
- **我收藏的歌单**: 用户收藏的其他用户公开歌单

> 注意：推荐歌单和热门歌单将在登录后的首页展示，不在用户歌单管理页面

### 4. 链接解析系统

#### 插件化架构
- 支持动态加载解析插件
- 统一的插件接口规范
- 插件配置管理
- 插件状态监控

#### 支持平台
- 网易云音乐
- QQ音乐
- 酷狗音乐
- 酷我音乐
- 咪咕音乐
- 可扩展其他平台

#### 解析功能
- URL自动识别平台
- 歌曲信息解析
- 播放链接获取
- 歌词信息获取
- 专辑封面获取

### 5. 搜索系统

#### 多平台搜索
- 分栏式搜索界面
- 支持平台：
  - 网易云音乐
  - QQ音乐
  - 酷狗音乐
  - 酷我音乐
  - 本站音乐
- 搜索结果统一格式化

### 6. 文件上传系统

#### 封面上传功能
- 歌单封面上传
- 用户头像上传
- 音乐专辑封面上传
- 图片格式支持 (JPG, PNG, WEBP)
- 图片尺寸和大小限制
- 自动图片压缩和优化

#### MinIO对象存储
- 所有文件统一存储在MinIO
- 音乐文件存储
- 图片文件存储
- 文件访问权限控制
- 存储桶分类管理

#### 搜索功能
- 歌曲名搜索
- 艺术家搜索
- 专辑搜索
- 歌词搜索
- 高级搜索过滤

## 技术架构

### 后端技术栈
- **运行环境**: Node.js 24.4.1
- **Web框架**: Express.js 5.1.0
- **数据库**: MongoDB 7.0 + Redis 7.2 (Docker容器)
- **ORM**: Mongoose 8.16.5
- **身份验证**: JWT + Passport.js
- **文件存储**: MinIO对象存储 (Docker容器)
- **音频处理**: FFmpeg + fluent-ffmpeg
- **容器化**: Docker + Docker Compose
- **开发环境**: 完全容器化的服务管理

### API设计规范
- RESTful API风格
- 统一的响应格式
- API版本控制 (v1, v2)
- 请求限流和防护
- API文档自动生成

#### API命名规范
```
GET    /api/v1/users              # 获取用户列表
POST   /api/v1/users              # 创建用户
GET    /api/v1/users/:id          # 获取用户详情
PUT    /api/v1/users/:id          # 更新用户信息
DELETE /api/v1/users/:id          # 删除用户

GET    /api/v1/music              # 获取音乐列表
POST   /api/v1/music              # 上传音乐
GET    /api/v1/music/:id          # 获取音乐详情
PUT    /api/v1/music/:id          # 更新音乐信息
DELETE /api/v1/music/:id          # 删除音乐

GET    /api/v1/playlists          # 获取歌单列表
POST   /api/v1/playlists          # 创建歌单
GET    /api/v1/playlists/:id      # 获取歌单详情
PUT    /api/v1/playlists/:id      # 更新歌单
DELETE /api/v1/playlists/:id      # 删除歌单

POST   /api/v1/parse-url          # 解析音乐链接
GET    /api/v1/search             # 搜索音乐

POST   /api/v1/upload/cover       # 上传封面图片
POST   /api/v1/upload/avatar      # 上传用户头像
GET    /api/v1/home/<USER>
GET    /api/v1/home/<USER>
```

### 数据库设计

#### 用户表 (users)
```javascript
{
  _id: ObjectId,
  username: String,
  email: String,
  password: String, // 加密存储
  avatar: String,
  userGroup: String, // admin, vip, normal
  points: Number,
  createdAt: Date,
  updatedAt: Date,
  lastLoginAt: Date
}
```

#### 音乐表 (music)
```javascript
{
  _id: ObjectId,
  title: String,
  artist: String,
  album: String,
  duration: Number,
  bitrate: Number,
  sampleRate: Number,
  fileSize: Number,
  filePath: String,
  coverImage: String,
  uploadedBy: ObjectId, // 用户ID
  quality: String, // standard, high, super, lossless
  status: String, // pending, approved, rejected
  createdAt: Date
}
```

#### 歌单表 (playlists)
```javascript
{
  _id: ObjectId,
  name: String,
  description: String,
  coverImage: String,
  isPublic: Boolean,
  isDefault: Boolean,
  createdBy: ObjectId, // 用户ID
  songs: [ObjectId], // 音乐ID数组
  tags: [String],
  playCount: Number,
  createdAt: Date,
  updatedAt: Date
}
```

## 项目结构

```
musicdou/
├── src/
│   ├── controllers/     # 控制器
│   ├── models/         # 数据模型
│   ├── routes/         # 路由定义
│   ├── middleware/     # 中间件
│   ├── services/       # 业务逻辑
│   ├── plugins/        # 解析插件
│   ├── utils/          # 工具函数
│   └── config/         # 配置文件
├── tests/              # 测试文件
├── docs/               # 文档
├── package.json
└── README.md

注意：不再需要本地uploads目录，所有文件都存储在MinIO中
```

## 开发计划与进度

### 第一阶段：基础环境搭建 ✅ **已完成**
- [x] **1.1 项目初始化** - 创建Node.js项目基础结构，配置开发环境
- [x] **1.2 依赖包安装和配置** - 安装项目所需的核心依赖包
- [x] **1.3 基础服务器搭建** - 创建Express服务器和基础中间件配置
- [x] **1.4 数据库连接配置** - 配置MongoDB和Redis数据库连接，迁移到Docker环境
- [x] **1.5 MinIO对象存储配置** - 配置MinIO客户端和存储桶，测试文件上传功能

### 第二阶段：用户系统开发 ✅ **已完成**
- [x] **2.1 用户数据模型设计** - 创建用户相关数据模型，包括用户信息、权限、积分等
- [x] **2.2 用户注册功能** - 实现用户注册接口，包括邮箱验证、密码加密等
- [x] **2.3 用户登录认证** - 实现JWT登录认证，包括token生成和验证
- [x] **2.4 用户权限管理** - 实现基于角色的权限控制系统
- [x] **2.5 用户积分系统** - 实现积分获取、消费、签到等功能

### 第三阶段：音乐管理系统 🔄 **进行中**
- [x] **3.1 音乐数据模型设计** - 创建音乐文件相关数据模型，定义音乐字段和索引
- [x] **3.2 音乐上传功能** - 实现音乐文件上传和处理，支持多种音频格式
- [x] **3.3 音频质量检测** - 使用FFmpeg分析音频文件质量，检测比特率和采样率 ⚡ **当前任务**
- [ ] **3.4 音乐元数据处理** - 提取和处理音乐文件元数据，包括ID3标签和封面图片
- [ ] **3.5 音乐管理接口** - 实现音乐的CRUD操作接口和审核功能

### 第四阶段：歌单系统开发 ⏳ **待开始**
- [ ] **4.1 歌单数据模型** - 设计歌单相关数据结构
- [ ] **4.2 歌单CRUD操作** - 实现歌单的创建、编辑、删除功能
- [ ] **4.3 歌单音乐管理** - 实现歌单内音乐的添加、移除、排序
- [ ] **4.4 歌单分享功能** - 实现歌单的公开分享和收藏功能

### 第五阶段：搜索和解析系统 ⏳ **待开始**
- [ ] **5.1 本地音乐搜索** - 实现站内音乐搜索功能
- [ ] **5.2 链接解析插件系统** - 设计可扩展的解析插件架构
- [ ] **5.3 多平台搜索功能** - 实现第三方平台音乐搜索
- [ ] **5.4 音乐链接解析** - 实现音乐链接的解析和获取

### 第六阶段：完善和部署 ⏳ **待开始**
- [ ] **6.1 单元测试和集成测试** - 编写完整的测试用例
- [ ] **6.2 API文档完善** - 生成完整的API文档
- [ ] **6.3 性能优化和缓存** - 优化系统性能，添加缓存机制
- [ ] **6.4 部署和监控** - 配置生产环境部署和监控

## 🎯 当前开发状态

### ✅ 已完成的核心功能
1. **完整的用户系统** - 注册、登录、权限管理、积分系统
2. **音乐上传功能** - 支持MP3、FLAC、WAV、AAC等格式
3. **音频元数据提取** - 自动提取歌曲信息、封面图片
4. **MinIO文件存储** - 完整的对象存储集成
5. **Docker容器化环境** - MongoDB、Redis、MinIO服务

### 🔄 正在开发的功能
- **音频质量检测服务** - 使用FFmpeg进行深度音频分析
- **音质等级自动分类** - 基于比特率、采样率的智能分类
- **音频完整性检查** - 检测音频文件是否损坏

### 📊 开发进度统计
- **总体进度**: 约 60% 完成
- **第一阶段**: 100% ✅
- **第二阶段**: 100% ✅
- **第三阶段**: 60% 🔄
- **第四阶段**: 0% ⏳
- **第五阶段**: 0% ⏳
- **第六阶段**: 0% ⏳

### 🚀 下一步计划
1. 完成音频质量检测功能的控制器和API接口
2. 增强音乐元数据处理，支持更多音频格式
3. 实现音乐审核和管理工作流
4. 开始歌单系统的开发

## 安全考虑

- 用户密码加密存储
- JWT Token安全管理
- API请求限流
- 文件上传安全检查
- SQL注入防护
- XSS攻击防护
- CORS配置

## 性能优化

- Redis缓存热点数据
- 数据库索引优化
- MinIO文件访问优化
- 图片压缩和懒加载
- API响应压缩
- 数据库连接池

## 监控和日志

- 系统性能监控
- 错误日志记录
- 用户行为分析
- API调用统计
- 服务器资源监控

## 🚀 快速开始 (Docker环境)

### 前置要求
- Node.js 24.4.1+ 和 npm
- Docker Desktop (已安装并运行)
- Git

### 1. 克隆项目
```bash
git clone <repository-url>
cd musicdou
```

### 2. 安装依赖
```bash
npm install
```

### 3. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件，配置必要的环境变量
```

### 4. 启动Docker服务
```bash
# 启动所有服务 (MongoDB, Redis, MinIO)
npm run docker:start

# 查看服务状态
npm run docker:status
```

### 5. 启动应用程序
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

### 6. 验证安装
```bash
# 检查健康状态
curl http://localhost:3000/health

# 检查API接口
curl http://localhost:3000/api/v1
```

### Docker管理命令
```bash
# 启动服务
npm run docker:start

# 停止服务
npm run docker:stop

# 重启服务
npm run docker:restart

# 查看日志
npm run docker:logs

# 查看状态
npm run docker:status

# 清理数据 (谨慎使用)
npm run docker:clean
```

### 服务访问地址
- **应用程序**: http://localhost:3000
- **MongoDB**: localhost:27017
- **Redis**: localhost:6379
- **MinIO Console**: http://localhost:9001 (minioadmin/musicdou123)
- **MinIO API**: http://localhost:9000

### 详细文档
- [Docker环境配置](docs/DOCKER.md) - 完整的Docker使用说明
- [开发任务清单](TASKS.md) - 项目开发进度跟踪
- [API文档](docs/API.md) - API接口文档 (开发中)

### 故障排除
1. **Docker服务启动失败**: 确保Docker Desktop已启动
2. **端口冲突**: 检查端口27017、6379、9000、9001是否被占用
3. **权限问题**: 确保脚本有执行权限 `chmod +x scripts/*.sh`
4. **环境变量**: 检查.env文件配置是否正确

更多问题请参考 [Docker文档](docs/DOCKER.md) 的故障排除部分。
