const MusicService = require('../services/musicService');

/**
 * 获取音乐列表
 */
const getMusicList = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      quality,
      genre,
      artist,
      sortBy = 'newest'
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      quality,
      genre,
      artist,
      sortBy
    };

    const result = await MusicService.getMusicList(options);

    res.status(200).json({
      success: true,
      message: 'Music list retrieved successfully',
      data: result
    });
  } catch (error) {
    console.error('Get music list error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get music list',
      error: error.message
    });
  }
};

/**
 * 获取音乐详情
 */
const getMusicById = async (req, res) => {
  try {
    const { id } = req.params;
    const includePrivate = req.user && (req.user.userGroup === 'admin' || req.user._id.toString() === req.query.uploadedBy);

    const music = await MusicService.getMusicById(id, includePrivate);

    res.status(200).json({
      success: true,
      message: 'Music details retrieved successfully',
      data: music
    });
  } catch (error) {
    console.error('Get music by ID error:', error);
    const statusCode = error.message === 'Music not found' ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
};

/**
 * 搜索音乐
 */
const searchMusic = async (req, res) => {
  try {
    const {
      q: query,
      page = 1,
      limit = 20,
      sortBy = 'relevance',
      quality,
      genre,
      artist
    } = req.query;

    if (!query || query.trim() === '') {
      return res.status(400).json({
        success: false,
        message: 'Search query is required'
      });
    }

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sortBy,
      quality,
      genre,
      artist
    };

    const result = await MusicService.searchMusic(query.trim(), options);

    res.status(200).json({
      success: true,
      message: 'Music search completed successfully',
      data: result
    });
  } catch (error) {
    console.error('Search music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to search music',
      error: error.message
    });
  }
};

/**
 * 更新音乐信息
 */
const updateMusic = async (req, res) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const userId = req.user._id;

    // 过滤不允许更新的字段
    const allowedFields = [
      'title', 'artist', 'album', 'genre', 'year',
      'tags', 'lyrics', 'hasLyrics'
    ];
    
    const filteredData = {};
    allowedFields.forEach(field => {
      if (updateData[field] !== undefined) {
        filteredData[field] = updateData[field];
      }
    });

    if (Object.keys(filteredData).length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No valid fields to update'
      });
    }

    const music = await MusicService.updateMusic(id, filteredData, userId);

    res.status(200).json({
      success: true,
      message: 'Music updated successfully',
      data: music
    });
  } catch (error) {
    console.error('Update music error:', error);
    const statusCode = error.message.includes('not found') ? 404 : 
                      error.message.includes('Permission denied') ? 403 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
};

/**
 * 删除音乐
 */
const deleteMusic = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user._id;

    await MusicService.deleteMusic(id, userId);

    res.status(200).json({
      success: true,
      message: 'Music deleted successfully'
    });
  } catch (error) {
    console.error('Delete music error:', error);
    const statusCode = error.message.includes('not found') ? 404 : 
                      error.message.includes('Permission denied') ? 403 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
};

/**
 * 审核音乐（管理员功能）
 */
const reviewMusic = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, reviewNote } = req.body;
    const reviewerId = req.user._id;

    // 验证状态
    if (!['approved', 'rejected'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be "approved" or "rejected"'
      });
    }

    const music = await MusicService.reviewMusic(id, status, reviewerId, reviewNote);

    res.status(200).json({
      success: true,
      message: `Music ${status} successfully`,
      data: music
    });
  } catch (error) {
    console.error('Review music error:', error);
    const statusCode = error.message.includes('not found') ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
};

/**
 * 获取音乐播放URL
 */
const getMusicPlayUrl = async (req, res) => {
  try {
    const { id } = req.params;
    const { expiry = 24 * 60 * 60 } = req.query; // 默认24小时

    const result = await MusicService.getMusicPlayUrl(id, parseInt(expiry));

    res.status(200).json({
      success: true,
      message: 'Play URL generated successfully',
      data: result
    });
  } catch (error) {
    console.error('Get play URL error:', error);
    const statusCode = error.message.includes('not found') ? 404 : 500;
    res.status(statusCode).json({
      success: false,
      message: error.message,
      error: error.message
    });
  }
};

/**
 * 获取热门音乐
 */
const getPopularMusic = async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const music = await MusicService.getPopularMusic(parseInt(limit));

    res.status(200).json({
      success: true,
      message: 'Popular music retrieved successfully',
      data: music
    });
  } catch (error) {
    console.error('Get popular music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get popular music',
      error: error.message
    });
  }
};

/**
 * 获取最新音乐
 */
const getRecentMusic = async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const music = await MusicService.getRecentMusic(parseInt(limit));

    res.status(200).json({
      success: true,
      message: 'Recent music retrieved successfully',
      data: music
    });
  } catch (error) {
    console.error('Get recent music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get recent music',
      error: error.message
    });
  }
};

/**
 * 获取音乐统计信息（管理员功能）
 */
const getMusicStats = async (req, res) => {
  try {
    const stats = await MusicService.getMusicStats();

    res.status(200).json({
      success: true,
      message: 'Music statistics retrieved successfully',
      data: stats
    });
  } catch (error) {
    console.error('Get music stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get music statistics',
      error: error.message
    });
  }
};

/**
 * 获取我上传的音乐
 */
const getMyMusic = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      sortBy = 'newest'
    } = req.query;

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      uploadedBy: req.user._id,
      status,
      sortBy
    };

    const result = await MusicService.getMusicList(options);

    res.status(200).json({
      success: true,
      message: 'My music list retrieved successfully',
      data: result
    });
  } catch (error) {
    console.error('Get my music error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get my music list',
      error: error.message
    });
  }
};

module.exports = {
  getMusicList,
  getMusicById,
  searchMusic,
  updateMusic,
  deleteMusic,
  reviewMusic,
  getMusicPlayUrl,
  getPopularMusic,
  getRecentMusic,
  getMusicStats,
  getMyMusic
};
