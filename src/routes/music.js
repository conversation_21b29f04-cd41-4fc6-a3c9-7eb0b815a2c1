const express = require('express');
const {
  getMusicList,
  getMusicById,
  searchMusic,
  updateMusic,
  deleteMusic,
  reviewMusic,
  getMusicPlayUrl,
  getPopularMusic,
  getRecentMusic,
  getMusicStats,
  getMyMusic
} = require('../controllers/musicController');
const { authenticateToken, requireAdmin } = require('../middleware/auth');

const router = express.Router();

// 公开接口（不需要认证）
router.get('/popular', getPopularMusic);
router.get('/recent', getRecentMusic);
router.get('/search', searchMusic);
router.get('/', getMusicList);
// 注意：/:id 路由要放在最后，避免拦截其他路由

// 需要认证的接口
router.get('/:id/play', authenticateToken, getMusicPlayUrl);
router.get('/my/uploads', authenticateToken, getMyMusic);

// 需要认证的修改接口
router.put('/:id', authenticateToken, updateMusic);
router.delete('/:id', authenticateToken, deleteMusic);

// 管理员接口
router.post('/:id/review', requireAdmin, reviewMusic);
router.get('/admin/stats', requireAdmin, getMusicStats);

// /:id 路由放在最后，避免拦截其他路由
router.get('/:id', getMusicById);

module.exports = router;
