const Music = require('../models/Music');
const { deleteFile, getPresignedUrl, BUCKETS } = require('../config/minio');

class MusicService {
  /**
   * 创建新的音乐记录
   * @param {Object} musicData - 音乐数据
   * @returns {Promise<Object>} 创建的音乐记录
   */
  static async createMusic(musicData) {
    try {
      const music = new Music(musicData);
      await music.save();
      
      // 填充上传者信息
      await music.populate('uploadedBy', 'username profile.displayName');
      
      return music;
    } catch (error) {
      throw new Error(`Failed to create music: ${error.message}`);
    }
  }

  /**
   * 根据ID获取音乐详情
   * @param {string} musicId - 音乐ID
   * @param {boolean} includePrivate - 是否包含未审核的音乐
   * @returns {Promise<Object>} 音乐详情
   */
  static async getMusicById(musicId, includePrivate = false) {
    try {
      let query = { _id: musicId };
      if (!includePrivate) {
        query.status = 'approved';
      }

      const music = await Music.findOne(query)
        .populate('uploadedBy', 'username profile.displayName')
        .populate('reviewedBy', 'username profile.displayName');

      if (!music) {
        throw new Error('Music not found');
      }

      return music;
    } catch (error) {
      throw new Error(`Failed to get music: ${error.message}`);
    }
  }

  /**
   * 获取音乐列表
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 音乐列表和分页信息
   */
  static async getMusicList(options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        status = 'approved',
        uploadedBy,
        quality,
        genre,
        artist,
        sortBy = 'newest'
      } = options;

      const skip = (page - 1) * limit;
      let query = {};

      // 状态过滤
      if (status) query.status = status;
      
      // 上传者过滤
      if (uploadedBy) query.uploadedBy = uploadedBy;
      
      // 音质过滤
      if (quality) query.quality = quality;
      
      // 流派过滤
      if (genre) query.genre = new RegExp(genre, 'i');
      
      // 艺术家过滤
      if (artist) query.artist = new RegExp(artist, 'i');

      // 排序
      let sort = {};
      switch (sortBy) {
        case 'newest':
          sort = { createdAt: -1 };
          break;
        case 'oldest':
          sort = { createdAt: 1 };
          break;
        case 'popular':
          sort = { playCount: -1, favoriteCount: -1 };
          break;
        case 'title':
          sort = { title: 1 };
          break;
        case 'artist':
          sort = { artist: 1, title: 1 };
          break;
        default:
          sort = { createdAt: -1 };
      }

      const [musicList, total] = await Promise.all([
        Music.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .populate('uploadedBy', 'username profile.displayName'),
        Music.countDocuments(query)
      ]);

      return {
        music: musicList,
        pagination: {
          current: page,
          total: Math.ceil(total / limit),
          count: musicList.length,
          totalCount: total
        }
      };
    } catch (error) {
      throw new Error(`Failed to get music list: ${error.message}`);
    }
  }

  /**
   * 搜索音乐
   * @param {string} query - 搜索关键词
   * @param {Object} options - 搜索选项
   * @returns {Promise<Object>} 搜索结果
   */
  static async searchMusic(query, options = {}) {
    try {
      const {
        page = 1,
        limit = 20,
        sortBy = 'relevance',
        quality,
        genre,
        artist
      } = options;

      const skip = (page - 1) * limit;
      
      const results = await Music.searchMusic(query, {
        limit,
        skip,
        sortBy,
        quality,
        genre,
        artist
      });

      const total = await Music.countDocuments({
        $text: { $search: query },
        status: 'approved',
        ...(quality && { quality }),
        ...(genre && { genre: new RegExp(genre, 'i') }),
        ...(artist && { artist: new RegExp(artist, 'i') })
      });

      return {
        music: results,
        pagination: {
          current: page,
          total: Math.ceil(total / limit),
          count: results.length,
          totalCount: total
        },
        query
      };
    } catch (error) {
      throw new Error(`Failed to search music: ${error.message}`);
    }
  }

  /**
   * 更新音乐信息
   * @param {string} musicId - 音乐ID
   * @param {Object} updateData - 更新数据
   * @param {string} userId - 操作用户ID
   * @returns {Promise<Object>} 更新后的音乐记录
   */
  static async updateMusic(musicId, updateData, userId) {
    try {
      const music = await Music.findById(musicId);
      if (!music) {
        throw new Error('Music not found');
      }

      // 检查权限（只有上传者或管理员可以修改）
      if (music.uploadedBy.toString() !== userId) {
        // 这里需要检查用户是否为管理员，暂时跳过
        // throw new Error('Permission denied');
      }

      Object.assign(music, updateData);
      await music.save();

      await music.populate('uploadedBy', 'username profile.displayName');
      return music;
    } catch (error) {
      throw new Error(`Failed to update music: ${error.message}`);
    }
  }

  /**
   * 删除音乐
   * @param {string} musicId - 音乐ID
   * @param {string} userId - 操作用户ID
   * @returns {Promise<boolean>} 删除结果
   */
  static async deleteMusic(musicId, userId) {
    try {
      const music = await Music.findById(musicId);
      if (!music) {
        throw new Error('Music not found');
      }

      // 检查权限
      if (music.uploadedBy.toString() !== userId) {
        // 这里需要检查用户是否为管理员，暂时跳过
        // throw new Error('Permission denied');
      }

      // 删除MinIO中的文件
      try {
        await deleteFile(music.bucket, music.filePath);
        
        // 如果有封面图片，也删除
        if (music.coverImage.objectName) {
          await deleteFile(music.coverImage.bucket, music.coverImage.objectName);
        }
      } catch (minioError) {
        console.warn('Failed to delete files from MinIO:', minioError.message);
        // 继续删除数据库记录，即使MinIO删除失败
      }

      await Music.findByIdAndDelete(musicId);
      return true;
    } catch (error) {
      throw new Error(`Failed to delete music: ${error.message}`);
    }
  }

  /**
   * 审核音乐
   * @param {string} musicId - 音乐ID
   * @param {string} status - 审核状态 (approved/rejected)
   * @param {string} reviewerId - 审核者ID
   * @param {string} reviewNote - 审核备注
   * @returns {Promise<Object>} 审核后的音乐记录
   */
  static async reviewMusic(musicId, status, reviewerId, reviewNote = null) {
    try {
      const music = await Music.findById(musicId);
      if (!music) {
        throw new Error('Music not found');
      }

      music.status = status;
      music.reviewedBy = reviewerId;
      music.reviewedAt = new Date();
      music.reviewNote = reviewNote;

      await music.save();
      await music.populate(['uploadedBy', 'reviewedBy'], 'username profile.displayName');

      return music;
    } catch (error) {
      throw new Error(`Failed to review music: ${error.message}`);
    }
  }

  /**
   * 获取音乐播放URL
   * @param {string} musicId - 音乐ID
   * @param {number} expiry - URL过期时间（秒）
   * @returns {Promise<string>} 播放URL
   */
  static async getMusicPlayUrl(musicId, expiry = 24 * 60 * 60) {
    try {
      const music = await Music.findOne({ _id: musicId, status: 'approved' });
      if (!music) {
        throw new Error('Music not found or not approved');
      }

      // 增加播放次数
      await music.incrementPlayCount();

      // 生成预签名URL
      const playUrl = await getPresignedUrl(music.bucket, music.filePath, expiry);
      
      return {
        playUrl,
        music: {
          id: music._id,
          title: music.title,
          artist: music.artist,
          album: music.album,
          duration: music.duration,
          coverImage: music.coverImage.url
        }
      };
    } catch (error) {
      throw new Error(`Failed to get play URL: ${error.message}`);
    }
  }

  /**
   * 获取热门音乐
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 热门音乐列表
   */
  static async getPopularMusic(limit = 10) {
    try {
      return await Music.getPopular(limit);
    } catch (error) {
      throw new Error(`Failed to get popular music: ${error.message}`);
    }
  }

  /**
   * 获取最新音乐
   * @param {number} limit - 限制数量
   * @returns {Promise<Array>} 最新音乐列表
   */
  static async getRecentMusic(limit = 10) {
    try {
      return await Music.getRecent(limit);
    } catch (error) {
      throw new Error(`Failed to get recent music: ${error.message}`);
    }
  }

  /**
   * 获取音乐统计信息
   * @returns {Promise<Object>} 统计信息
   */
  static async getMusicStats() {
    try {
      const [
        totalMusic,
        approvedMusic,
        pendingMusic,
        rejectedMusic,
        totalPlayCount,
        totalDownloadCount
      ] = await Promise.all([
        Music.countDocuments(),
        Music.countDocuments({ status: 'approved' }),
        Music.countDocuments({ status: 'pending' }),
        Music.countDocuments({ status: 'rejected' }),
        Music.aggregate([{ $group: { _id: null, total: { $sum: '$playCount' } } }]),
        Music.aggregate([{ $group: { _id: null, total: { $sum: '$downloadCount' } } }])
      ]);

      return {
        totalMusic,
        approvedMusic,
        pendingMusic,
        rejectedMusic,
        totalPlayCount: totalPlayCount[0]?.total || 0,
        totalDownloadCount: totalDownloadCount[0]?.total || 0
      };
    } catch (error) {
      throw new Error(`Failed to get music stats: ${error.message}`);
    }
  }
}

module.exports = MusicService;
