const { parseBuffer } = require('music-metadata');
const path = require('path');

class AudioMetadataService {
  /**
   * 从音频文件缓冲区提取元数据
   * @param {Buffer} buffer - 音频文件缓冲区
   * @param {string} mimeType - 文件MIME类型
   * @returns {Promise<Object>} 提取的元数据
   */
  static async extractMetadata(buffer, mimeType) {
    try {
      const metadata = await parseBuffer(buffer, mimeType);
      
      return {
        // 基本信息
        title: metadata.common.title || null,
        artist: metadata.common.artist || null,
        album: metadata.common.album || null,
        genre: metadata.common.genre?.[0] || null,
        year: metadata.common.year || null,

        // 音频技术信息
        duration: Math.max(0, metadata.format.duration || 0),
        bitrate: Math.min(9999, Math.max(32, metadata.format.bitrate || 128)), // 限制在合理范围内
        sampleRate: Math.max(0, metadata.format.sampleRate || 44100),
        channels: Math.max(1, metadata.format.numberOfChannels || 2),
        
        // 文件格式信息
        container: metadata.format.container || null,
        codec: metadata.format.codec || null,
        lossless: metadata.format.lossless || false,
        
        // 封面图片
        picture: metadata.common.picture?.[0] || null,
        
        // 其他信息
        albumartist: metadata.common.albumartist || null,
        track: metadata.common.track?.no || null,
        disk: metadata.common.disk?.no || null,
        comment: metadata.common.comment?.[0] || null,
        lyrics: metadata.common.lyrics?.[0] || null
      };
    } catch (error) {
      console.error('Error extracting audio metadata:', error);
      throw new Error(`Failed to extract metadata: ${error.message}`);
    }
  }

  /**
   * 根据文件扩展名确定音质等级
   * @param {string} fileFormat - 文件格式
   * @param {number} bitrate - 比特率
   * @param {boolean} lossless - 是否无损
   * @returns {string} 音质等级
   */
  static determineQuality(fileFormat, bitrate, lossless) {
    // 无损格式
    if (lossless || ['flac', 'wav', 'aiff'].includes(fileFormat.toLowerCase())) {
      return 'lossless';
    }
    
    // 根据比特率判断
    if (bitrate >= 320) {
      return 'super';
    } else if (bitrate >= 192) {
      return 'high';
    } else {
      return 'standard';
    }
  }

  /**
   * 验证音频文件格式
   * @param {string} originalName - 原始文件名
   * @param {string} mimeType - MIME类型
   * @returns {Object} 验证结果
   */
  static validateAudioFile(originalName, mimeType) {
    const allowedFormats = (process.env.ALLOWED_AUDIO_FORMATS || 'mp3,flac,wav,aac').split(',');
    const fileExtension = path.extname(originalName).toLowerCase().substring(1);
    
    // 检查文件扩展名
    if (!allowedFormats.includes(fileExtension)) {
      return {
        valid: false,
        error: `Unsupported file format: ${fileExtension}. Allowed formats: ${allowedFormats.join(', ')}`
      };
    }
    
    // 检查MIME类型
    const validMimeTypes = {
      'mp3': ['audio/mpeg', 'audio/mp3'],
      'flac': ['audio/flac', 'audio/x-flac'],
      'wav': ['audio/wav', 'audio/wave', 'audio/x-wav'],
      'aac': ['audio/aac', 'audio/x-aac'],
      'm4a': ['audio/mp4', 'audio/x-m4a'],
      'ogg': ['audio/ogg', 'audio/vorbis']
    };
    
    const expectedMimeTypes = validMimeTypes[fileExtension] || [];
    if (expectedMimeTypes.length > 0 && !expectedMimeTypes.includes(mimeType)) {
      console.warn(`MIME type mismatch: expected ${expectedMimeTypes.join(' or ')}, got ${mimeType}`);
      // 不阻止上传，只是警告
    }
    
    return {
      valid: true,
      format: fileExtension
    };
  }

  /**
   * 处理封面图片
   * @param {Object} picture - 图片元数据
   * @returns {Object} 处理后的图片信息
   */
  static processCoverImage(picture) {
    if (!picture || !picture.data) {
      return null;
    }
    
    try {
      return {
        format: picture.format || 'image/jpeg',
        data: picture.data,
        description: picture.description || 'Cover',
        size: picture.data.length
      };
    } catch (error) {
      console.error('Error processing cover image:', error);
      return null;
    }
  }

  /**
   * 清理和标准化元数据
   * @param {Object} metadata - 原始元数据
   * @param {string} originalName - 原始文件名
   * @returns {Object} 清理后的元数据
   */
  static cleanMetadata(metadata, originalName) {
    // 如果没有标题，使用文件名（去掉扩展名）
    const title = metadata.title || 
                  path.basename(originalName, path.extname(originalName));
    
    // 如果没有艺术家，使用默认值
    const artist = metadata.artist || 'Unknown Artist';
    
    // 清理字符串字段
    const cleanString = (str) => {
      if (!str) return null;
      return str.toString().trim().substring(0, 200); // 限制长度
    };
    
    return {
      title: cleanString(title),
      artist: cleanString(artist),
      album: cleanString(metadata.album),
      genre: cleanString(metadata.genre),
      year: metadata.year && metadata.year > 1900 && metadata.year <= new Date().getFullYear() + 1 
            ? metadata.year : null,
      duration: Math.max(0, metadata.duration || 0),
      bitrate: Math.max(0, metadata.bitrate || 0),
      sampleRate: Math.max(0, metadata.sampleRate || 0),
      channels: Math.max(1, metadata.channels || 2),
      container: metadata.container,
      codec: metadata.codec,
      lossless: Boolean(metadata.lossless),
      picture: metadata.picture,
      albumartist: cleanString(metadata.albumartist),
      track: metadata.track && metadata.track > 0 ? metadata.track : null,
      disk: metadata.disk && metadata.disk > 0 ? metadata.disk : null,
      comment: cleanString(metadata.comment),
      lyrics: metadata.lyrics ? metadata.lyrics.substring(0, 10000) : null // 限制歌词长度
    };
  }

  /**
   * 生成音乐文件的完整信息
   * @param {Buffer} buffer - 文件缓冲区
   * @param {Object} fileInfo - 文件基本信息
   * @returns {Promise<Object>} 完整的音乐信息
   */
  static async generateMusicInfo(buffer, fileInfo) {
    try {
      const { originalName, fileName, mimeType, size } = fileInfo;
      
      // 验证文件格式
      const validation = this.validateAudioFile(originalName, mimeType);
      if (!validation.valid) {
        throw new Error(validation.error);
      }
      
      // 提取元数据
      const rawMetadata = await this.extractMetadata(buffer, mimeType);
      
      // 清理元数据
      const metadata = this.cleanMetadata(rawMetadata, originalName);
      
      // 确定音质等级
      const quality = this.determineQuality(validation.format, metadata.bitrate, metadata.lossless);
      
      // 处理封面图片
      const coverImage = this.processCoverImage(metadata.picture);
      
      return {
        // 基本信息
        title: metadata.title,
        artist: metadata.artist,
        album: metadata.album,
        genre: metadata.genre,
        year: metadata.year,
        
        // 音频技术信息
        duration: metadata.duration,
        bitrate: metadata.bitrate,
        sampleRate: metadata.sampleRate,
        channels: metadata.channels,
        
        // 文件信息
        fileSize: size,
        fileName: fileName,
        originalName: originalName,
        fileFormat: validation.format,
        mimeType: mimeType,
        quality: quality,
        
        // 其他信息
        tags: [], // 可以后续添加标签
        lyrics: metadata.lyrics,
        hasLyrics: Boolean(metadata.lyrics),
        
        // 封面图片信息
        coverImage: coverImage,
        
        // 技术细节
        container: metadata.container,
        codec: metadata.codec,
        lossless: metadata.lossless
      };
    } catch (error) {
      console.error('Error generating music info:', error);
      throw new Error(`Failed to process audio file: ${error.message}`);
    }
  }
}

module.exports = AudioMetadataService;
